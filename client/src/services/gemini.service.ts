import { GoogleGenerativeAI } from '@google/generative-ai';

class GeminiService {
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor() {
    // Use the API key from user's memory
    const apiKey = 'AIzaSyDP_EbWxdRm0R9qEFU2KXwpRoDppCRAaiw';
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.0-flash-exp' });
  }

  async enhanceDescription(title: string, subtitle?: string, description?: string): Promise<string> {
    try {
      const prompt = `
        You are an expert course description writer for premium online learning platforms like Udemy, Coursera, and MasterClass.
        
        Course Title: "${title}"
        ${subtitle ? `Subtitle: "${subtitle}"` : ''}
        ${description ? `Current Description: "${description}"` : ''}
        
        Create a compelling, professional course description that:
        
        STRUCTURE:
        1. Hook: Start with a powerful opening that addresses the learner's pain point or aspiration
        2. What You'll Learn: 4-6 specific, measurable learning outcomes
        3. Course Content: Brief overview of key topics and methodologies
        4. Who This Is For: Target audience and prerequisites
        5. Why Choose This Course: Unique value proposition and instructor credibility
        6. Call to Action: Motivational closing statement
        
        REQUIREMENTS:
        - 300-500 words total
        - Use specific technologies, tools, and methodologies mentioned in the title
        - Include measurable outcomes and real-world applications
        - Emphasize career advancement and industry relevance
        - Use active voice and action-oriented language
        - Avoid generic phrases and focus on unique value
        - Include social proof elements when relevant
        - Format with proper HTML tags for rich text display
        
        FORMATTING:
        - Use <h3> for section headers
        - Use <ul> and <li> for bullet points
        - Use <strong> for emphasis
        - Use <p> for paragraphs
        - Ensure proper HTML structure
        
        TONE: Professional, confident, results-focused, inspiring
        
        Return only the formatted HTML description, no quotes or explanations.
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text().trim();
    } catch (error) {
      console.error('Gemini description enhancement error:', error);
      throw new Error('Failed to enhance description with AI');
    }
  }

  async generateDescription(title: string, subtitle?: string): Promise<string> {
    try {
      const prompt = `
        You are an expert course description writer for premium online learning platforms.
        
        Course Title: "${title}"
        ${subtitle ? `Subtitle: "${subtitle}"` : ''}
        
        Create a compelling, professional course description from scratch that:
        
        STRUCTURE:
        1. Hook: Start with a powerful opening that addresses the learner's pain point
        2. What You'll Learn: 5-7 specific, measurable learning outcomes
        3. Course Content: Overview of key topics and practical projects
        4. Who This Is For: Target audience description
        5. Why Choose This Course: Unique value proposition
        6. Call to Action: Motivational closing
        
        REQUIREMENTS:
        - 350-450 words total
        - Infer content from the title and subtitle
        - Include specific technologies and tools
        - Emphasize practical, hands-on learning
        - Mention real-world projects and portfolio building
        - Use industry-relevant keywords
        - Format with proper HTML tags
        
        FORMATTING:
        - Use <h3> for section headers
        - Use <ul> and <li> for learning outcomes
        - Use <strong> for key terms
        - Use <p> for paragraphs
        - Ensure clean HTML structure
        
        TONE: Professional, exciting, results-oriented
        
        Return only the formatted HTML description.
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text().trim();
    } catch (error) {
      console.error('Gemini description generation error:', error);
      throw new Error('Failed to generate description with AI');
    }
  }

  async improveDescription(currentDescription: string, title: string, subtitle?: string): Promise<string> {
    try {
      const prompt = `
        You are an expert course description optimizer for online learning platforms.
        
        Course Title: "${title}"
        ${subtitle ? `Subtitle: "${subtitle}"` : ''}
        Current Description: "${currentDescription}"
        
        Improve the existing description by:
        
        ENHANCEMENTS:
        - Making it more engaging and compelling
        - Adding specific learning outcomes if missing
        - Improving clarity and readability
        - Adding industry-relevant keywords
        - Enhancing the value proposition
        - Improving the structure and flow
        - Adding HTML formatting for better presentation
        
        MAINTAIN:
        - The core message and intent
        - Any specific details already mentioned
        - The approximate length (300-500 words)
        
        FORMATTING:
        - Use proper HTML tags (<h3>, <p>, <ul>, <li>, <strong>)
        - Ensure clean, semantic HTML structure
        - Make it visually appealing when rendered
        
        Return only the improved HTML description.
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text().trim();
    } catch (error) {
      console.error('Gemini description improvement error:', error);
      throw new Error('Failed to improve description with AI');
    }
  }
}

export const geminiService = new GeminiService();
